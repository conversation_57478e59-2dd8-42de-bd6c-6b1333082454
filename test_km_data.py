#!/usr/bin/env python3
"""
测试处理Km_Data.xlsx文件的前几条数据
"""

import pandas as pd
import numpy as np
import torch
import os
import sys
from pathlib import Path
import logging
from tqdm import tqdm

# 导入attention_fusion模块
from feature_extractor import ProteinFeatureExtractor
from attention_fusion_model import AttentionFusionModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_small_batch():
    """测试处理前10条数据"""
    logger.info("测试处理前10条数据")
    
    # 读取数据
    input_file = "../20250623_Data/Km_Data.xlsx"
    df = pd.read_excel(input_file)
    
    # 取前10条数据
    test_df = df[['ID', 'Sequence']].head(10).copy()
    logger.info(f"测试数据形状: {test_df.shape}")
    
    # 初始化模型
    logger.info("初始化模型...")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    extractor = ProteinFeatureExtractor()
    model = AttentionFusionModel(esm_dim=1280, msa_dim=768, hidden_dim=512, num_blocks=2, num_heads=4)
    model.to(device)
    model.eval()
    
    # 处理每个序列
    results = []
    
    for idx, row in test_df.iterrows():
        seq_id = row['ID']
        sequence = row['Sequence']
        
        logger.info(f"处理序列 {seq_id}, 长度: {len(sequence)}")
        
        try:
            # 截取序列长度
            if len(sequence) > 500:
                sequence = sequence[:500]
                logger.info(f"  序列截取到500个氨基酸")
            
            # 提取融合特征
            fusion_results = model.extract_and_fuse(sequence, extractor)
            
            # 获取融合特征向量
            fused_features = fusion_results["fused_features"]  # [1, seq_len, 512]
            sequence_representation = fused_features.mean(dim=1).squeeze(0)  # [512]
            feature_vector = sequence_representation.detach().cpu().numpy()
            
            results.append({
                'ID': seq_id,
                'sequence_length': len(sequence),
                'fusion_vector': feature_vector,
                'vector_shape': feature_vector.shape,
                'vector_mean': float(feature_vector.mean()),
                'vector_std': float(feature_vector.std()),
                'success': True
            })
            
            logger.info(f"  ✅ 成功提取特征向量: {feature_vector.shape}")
            logger.info(f"  特征统计: 均值={feature_vector.mean():.4f}, 标准差={feature_vector.std():.4f}")
            
        except Exception as e:
            logger.error(f"  ❌ 处理失败: {str(e)}")
            results.append({
                'ID': seq_id,
                'sequence_length': len(sequence),
                'fusion_vector': None,
                'success': False,
                'error': str(e)
            })
    
    # 统计结果
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    
    logger.info(f"测试结果:")
    logger.info(f"  成功: {len(successful)}/{len(results)}")
    logger.info(f"  失败: {len(failed)}/{len(results)}")
    
    if successful:
        logger.info(f"  特征维度: {successful[0]['vector_shape']}")
        avg_mean = np.mean([r['vector_mean'] for r in successful])
        avg_std = np.mean([r['vector_std'] for r in successful])
        logger.info(f"  平均特征统计: 均值={avg_mean:.4f}, 标准差={avg_std:.4f}")
    
    # 保存测试结果
    output_data = []
    for result in results:
        row = {
            'ID': result['ID'],
            'sequence_length': result['sequence_length'],
            'success': result['success']
        }
        
        if result['success']:
            # 将特征向量转换为字符串
            feature_vector = result['fusion_vector']
            row['fusion_vector'] = ','.join(map(str, feature_vector))
            row['vector_dim'] = len(feature_vector)
            row['vector_mean'] = result['vector_mean']
            row['vector_std'] = result['vector_std']
        else:
            row['fusion_vector'] = None
            row['vector_dim'] = None
            row['error'] = result.get('error', 'Unknown error')
        
        output_data.append(row)
    
    # 保存测试结果
    test_output = "../20250623_Data/Km_Data_test_results.csv"
    test_df_output = pd.DataFrame(output_data)
    test_df_output.to_csv(test_output, index=False)
    
    logger.info(f"测试结果已保存到: {test_output}")
    
    return len(successful) == len(results)

if __name__ == "__main__":
    success = test_small_batch()
    if success:
        logger.info("🎉 测试成功！可以处理完整数据集")
    else:
        logger.error("❌ 测试失败，请检查问题")

#!/usr/bin/env python3
"""
处理KcatData_20250625.xlsx文件，使用attention fusion方法提取蛋白质序列的融合特征
基于seq2attention模块和现有的attention fusion框架
"""

import pandas as pd
import numpy as np
import torch
import os
import sys
from pathlib import Path
import logging
from tqdm import tqdm
import json
from datetime import datetime
import time

# 导入seq2attention模块
from seq2attention import seq2attention, batch_seq2attention, get_model_info

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class KcatDataProcessor:
    def __init__(self):
        self.input_file = "KcatData_20250625.xlsx"
        self.output_file = "kcatData_20250625.csv"
        self.batch_size = 10  # 批量处理大小，避免内存溢出
        self.checkpoint_interval = 100  # 每100条保存一次检查点
        
    def load_data(self):
        """加载KcatData数据"""
        logger.info("加载KcatData_20250625.xlsx文件...")
        
        if not os.path.exists(self.input_file):
            logger.error(f"输入文件不存在: {self.input_file}")
            return None
        
        try:
            df = pd.read_excel(self.input_file)
            logger.info(f"数据加载成功: {df.shape}")
            logger.info(f"列名: {list(df.columns)}")
            
            # 检查必需的列
            required_cols = ['ID', 'Sequence']
            for col in required_cols:
                if col not in df.columns:
                    logger.error(f"数据文件中缺少列: {col}")
                    return None
            
            # 提取需要的列
            work_df = df[['ID', 'Sequence']].copy()
            
            # 序列长度统计
            seq_lengths = work_df['Sequence'].str.len()
            logger.info(f"序列长度统计: 最短={seq_lengths.min()}, 最长={seq_lengths.max()}, 平均={seq_lengths.mean():.1f}")
            
            # 检查无效序列
            invalid_mask = work_df['Sequence'].isna() | (work_df['Sequence'].str.len() < 8)
            invalid_count = invalid_mask.sum()
            if invalid_count > 0:
                logger.warning(f"发现 {invalid_count} 条无效序列，将被跳过")
                work_df = work_df[~invalid_mask].reset_index(drop=True)
            
            logger.info(f"有效序列数量: {len(work_df)}")
            return work_df
            
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            return None
    
    def process_single_sequence(self, seq_id, sequence):
        """处理单个序列"""
        try:
            # 检查序列长度 - ESM-2最大允许长度1024
            if len(sequence) > 1024:
                logger.warning(f"序列 {seq_id} 长度 {len(sequence)} 超过1024，截取前1024个氨基酸")
                sequence = sequence[:1024]
            
            # 使用seq2attention函数提取特征
            feature_vector = seq2attention(sequence, verbose=False)
            
            # 转换为逗号分隔的字符串
            vector_str = ','.join(map(str, feature_vector))
            
            return {
                'ID': seq_id,
                'Sequence': sequence,
                'attention_vector': vector_str,
                'success': True,
                'error': None,
                'vector_dim': len(feature_vector)
            }
            
        except Exception as e:
            logger.error(f"处理序列 {seq_id} 时出错: {str(e)}")
            return {
                'ID': seq_id,
                'Sequence': sequence,
                'attention_vector': None,
                'success': False,
                'error': str(e),
                'vector_dim': 0
            }
    
    def save_checkpoint(self, results, checkpoint_num):
        """保存检查点"""
        checkpoint_file = f"kcat_checkpoint_{checkpoint_num}.csv"
        temp_df = pd.DataFrame(results)
        temp_df.to_csv(checkpoint_file, index=False)
        logger.info(f"检查点已保存: {checkpoint_file}")
    
    def process_kcat_data(self):
        """处理KcatData文件"""
        logger.info("开始处理KcatData_20250625.xlsx文件")
        
        # 加载数据
        work_df = self.load_data()
        if work_df is None:
            return
        
        total_sequences = len(work_df)
        logger.info(f"总共需要处理 {total_sequences} 条序列")
        
        # 显示模型信息
        model_info = get_model_info()
        logger.info("使用的模型配置:")
        for key, value in model_info.items():
            logger.info(f"  {key}: {value}")
        
        # 处理每个序列
        results = []
        start_time = time.time()
        
        logger.info("开始提取attention融合特征...")
        
        for idx, row in tqdm(work_df.iterrows(), total=len(work_df), desc="处理序列"):
            seq_id = row['ID']
            sequence = row['Sequence']
            
            # 处理序列
            result = self.process_single_sequence(seq_id, sequence)
            results.append(result)
            
            # 显示进度
            if (idx + 1) % 50 == 0:
                elapsed_time = time.time() - start_time
                avg_time_per_seq = elapsed_time / (idx + 1)
                remaining_time = avg_time_per_seq * (total_sequences - idx - 1)
                
                success_count = sum(1 for r in results if r['success'])
                success_rate = success_count / len(results) * 100
                
                logger.info(f"进度: {idx + 1}/{total_sequences} ({(idx + 1)/total_sequences*100:.1f}%)")
                logger.info(f"成功率: {success_rate:.1f}% ({success_count}/{len(results)})")
                logger.info(f"预计剩余时间: {remaining_time/60:.1f} 分钟")
            
            # 保存检查点
            if (idx + 1) % self.checkpoint_interval == 0:
                self.save_checkpoint(results, idx + 1)
        
        # 创建输出数据框
        output_df = pd.DataFrame(results)
        
        # 保存最终结果
        logger.info(f"保存结果到 {self.output_file}")
        output_df.to_csv(self.output_file, index=False)
        
        # 统计信息
        successful = sum(1 for r in results if r['success'])
        failed = len(results) - successful
        success_rate = successful / len(results) * 100 if results else 0
        
        total_time = time.time() - start_time
        avg_time_per_seq = total_time / len(results) if results else 0
        
        logger.info("=" * 60)
        logger.info("处理完成!")
        logger.info(f"总序列数: {len(results)}")
        logger.info(f"成功处理: {successful}")
        logger.info(f"处理失败: {failed}")
        logger.info(f"成功率: {success_rate:.1f}%")
        logger.info(f"总耗时: {total_time/60:.1f} 分钟")
        logger.info(f"平均每序列: {avg_time_per_seq:.2f} 秒")
        
        if successful > 0:
            # 获取特征维度
            first_success = next(r for r in results if r['success'])
            if first_success['attention_vector']:
                feature_dim = len(first_success['attention_vector'].split(','))
                logger.info(f"特征维度: {feature_dim}")
        
        logger.info(f"结果已保存到: {self.output_file}")
        
        # 清理检查点文件
        self.cleanup_checkpoints()
    
    def cleanup_checkpoints(self):
        """清理检查点文件"""
        checkpoint_files = list(Path('.').glob('kcat_checkpoint_*.csv'))
        for file in checkpoint_files:
            try:
                file.unlink()
                logger.info(f"已删除检查点文件: {file}")
            except Exception as e:
                logger.warning(f"删除检查点文件失败: {file}, 错误: {e}")

def main():
    """主函数"""
    print("KcatData_20250625.xlsx 处理工具")
    print("使用attention fusion方法提取蛋白质序列特征")
    print("=" * 60)
    
    # 创建处理器
    processor = KcatDataProcessor()
    
    # 处理数据
    try:
        processor.process_kcat_data()
    except KeyboardInterrupt:
        logger.info("用户中断处理")
        print("\n处理被用户中断")
    except Exception as e:
        logger.error(f"处理过程中出现错误: {e}")
        print(f"\n[ERROR] 处理失败: {e}")

if __name__ == "__main__":
    main()

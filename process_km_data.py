#!/usr/bin/env python3
"""
处理Km_Data.xlsx文件，计算attention_fusion特征向量
"""

import pandas as pd
import numpy as np
import torch
import os
import sys
from pathlib import Path
import logging
from tqdm import tqdm
import pickle
import json

# 导入attention_fusion模块
from feature_extractor import ProteinFeatureExtractor
from attention_fusion_model import AttentionFusionModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_models():
    """初始化模型"""
    logger.info("初始化attention_fusion模型...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 初始化特征提取器
    extractor = ProteinFeatureExtractor(
        esm_model_name="facebook/esm2_t33_650M_UR50D",
        msa_model_name="facebook/esm_msa1b_t12_100M_UR50S"
    )
    
    # 初始化融合模型
    model = AttentionFusionModel(
        esm_dim=1280, 
        msa_dim=768,  # 使用Facebook MSA的768维
        hidden_dim=512, 
        num_blocks=2, 
        num_heads=4
    )
    model.to(device)
    model.eval()
    
    logger.info("[SUCCESS] 模型初始化完成")
    return extractor, model, device

def extract_fusion_features(sequence, extractor, model, device):
    """提取单个序列的融合特征"""
    try:
        # 检查序列长度
        if len(sequence) > 1000:
            logger.warning(f"序列长度 {len(sequence)} 超过1000，截取前1000个氨基酸")
            sequence = sequence[:1000]
        
        # 提取融合特征
        results = model.extract_and_fuse(sequence, extractor)
        
        # 获取融合特征 (batch_size, seq_len, hidden_dim)
        fused_features = results["fused_features"]  # [1, seq_len, 512]
        
        # 计算序列级表示 (平均池化)
        sequence_representation = fused_features.mean(dim=1).squeeze(0)  # [512]
        
        # 转换为numpy数组
        feature_vector = sequence_representation.detach().cpu().numpy()
        
        return feature_vector, True, None
        
    except Exception as e:
        logger.error(f"处理序列时出错: {str(e)}")
        return None, False, str(e)

def process_batch(sequences, ids, extractor, model, device, batch_size=1):
    """批量处理序列"""
    results = []
    
    for i in tqdm(range(0, len(sequences), batch_size), desc="处理序列"):
        batch_sequences = sequences[i:i+batch_size]
        batch_ids = ids[i:i+batch_size]
        
        for seq_id, sequence in zip(batch_ids, batch_sequences):
            # 检查序列是否有效
            if pd.isna(sequence) or len(sequence) < 8:
                logger.warning(f"序列 {seq_id} 无效，跳过")
                results.append({
                    'ID': seq_id,
                    'fusion_vector': None,
                    'success': False,
                    'error': 'Invalid sequence'
                })
                continue
            
            # 提取特征
            feature_vector, success, error = extract_fusion_features(
                sequence, extractor, model, device
            )
            
            results.append({
                'ID': seq_id,
                'fusion_vector': feature_vector,
                'success': success,
                'error': error
            })
            
            # 每处理100个序列保存一次中间结果
            if len(results) % 100 == 0:
                logger.info(f"已处理 {len(results)} 个序列")
    
    return results

def save_intermediate_results(results, output_path):
    """保存中间结果"""
    intermediate_path = output_path.replace('.csv', '_intermediate.pkl')
    with open(intermediate_path, 'wb') as f:
        pickle.dump(results, f)
    logger.info(f"中间结果已保存到: {intermediate_path}")

def load_intermediate_results(output_path):
    """加载中间结果"""
    intermediate_path = output_path.replace('.csv', '_intermediate.pkl')
    if os.path.exists(intermediate_path):
        with open(intermediate_path, 'rb') as f:
            results = pickle.load(f)
        logger.info(f"从中间结果恢复: {len(results)} 个序列")
        return results
    return None

def main():
    """主函数"""
    logger.info("开始处理Km_Data.xlsx文件")
    
    # 文件路径
    input_file = "../20250623_Data/Km_Data.xlsx"
    output_dir = "../20250623_Data"
    output_file = os.path.join(output_dir, "Km_Data_fusion_vector.csv")
    
    # 检查输入文件
    if not os.path.exists(input_file):
        logger.error(f"输入文件不存在: {input_file}")
        return
    
    # 读取数据
    logger.info("读取Excel文件...")
    df = pd.read_excel(input_file)
    logger.info(f"数据形状: {df.shape}")
    
    # 提取ID和Sequence列
    if 'ID' not in df.columns or 'Sequence' not in df.columns:
        logger.error("数据文件中缺少ID或Sequence列")
        return
    
    # 创建工作数据框
    work_df = df[['ID', 'Sequence']].copy()
    logger.info(f"提取到 {len(work_df)} 条序列数据")
    
    # 序列长度统计
    seq_lengths = work_df['Sequence'].str.len()
    logger.info(f"序列长度统计: 最短={seq_lengths.min()}, 最长={seq_lengths.max()}, 平均={seq_lengths.mean():.1f}")
    
    # 检查是否有中间结果
    intermediate_results = load_intermediate_results(output_file)
    
    if intermediate_results is None:
        # 初始化模型
        extractor, model, device = setup_models()
        
        # 处理序列
        logger.info("开始提取融合特征...")
        results = process_batch(
            work_df['Sequence'].tolist(),
            work_df['ID'].tolist(),
            extractor, model, device,
            batch_size=1  # 由于序列长度差异大，使用batch_size=1
        )
        
        # 保存中间结果
        save_intermediate_results(results, output_file)
    else:
        results = intermediate_results
    
    # 处理结果
    logger.info("处理结果...")
    
    successful_results = [r for r in results if r['success']]
    failed_results = [r for r in results if not r['success']]
    
    logger.info(f"成功处理: {len(successful_results)} 个序列")
    logger.info(f"处理失败: {len(failed_results)} 个序列")
    
    if failed_results:
        logger.info("失败原因统计:")
        error_counts = {}
        for r in failed_results:
            error = r['error'] or 'Unknown'
            error_counts[error] = error_counts.get(error, 0) + 1
        for error, count in error_counts.items():
            logger.info(f"  {error}: {count} 个")
    
    # 创建输出数据框
    output_data = []
    
    for result in results:
        row = {
            'ID': result['ID'],
            'success': result['success']
        }
        
        if result['success'] and result['fusion_vector'] is not None:
            # 将特征向量转换为字符串格式保存
            feature_vector = result['fusion_vector']
            row['fusion_vector'] = ','.join(map(str, feature_vector))
            row['vector_dim'] = len(feature_vector)
        else:
            row['fusion_vector'] = None
            row['vector_dim'] = None
            row['error'] = result['error']
        
        output_data.append(row)
    
    # 创建输出DataFrame
    output_df = pd.DataFrame(output_data)
    
    # 保存结果
    logger.info(f"保存结果到: {output_file}")
    output_df.to_csv(output_file, index=False)
    
    # 保存统计信息
    stats = {
        'total_sequences': len(results),
        'successful': len(successful_results),
        'failed': len(failed_results),
        'success_rate': len(successful_results) / len(results) * 100,
        'feature_dimension': 512 if successful_results else None,
        'average_sequence_length': float(seq_lengths.mean()),
        'min_sequence_length': int(seq_lengths.min()),
        'max_sequence_length': int(seq_lengths.max())
    }
    
    stats_file = output_file.replace('.csv', '_stats.json')
    with open(stats_file, 'w') as f:
        json.dump(stats, f, indent=2)
    
    logger.info("处理完成!")
    logger.info(f"统计信息:")
    logger.info(f"  总序列数: {stats['total_sequences']}")
    logger.info(f"  成功处理: {stats['successful']}")
    logger.info(f"  处理失败: {stats['failed']}")
    logger.info(f"  成功率: {stats['success_rate']:.1f}%")
    logger.info(f"  特征维度: {stats['feature_dimension']}")
    
    # 清理中间文件
    intermediate_path = output_file.replace('.csv', '_intermediate.pkl')
    if os.path.exists(intermediate_path):
        os.remove(intermediate_path)
        logger.info("已清理中间文件")

if __name__ == "__main__":
    main()

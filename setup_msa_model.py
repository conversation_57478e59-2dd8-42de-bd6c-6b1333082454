#!/usr/bin/env python3
"""
MSA Transformer模型下载和设置脚本
"""

import os
import shutil
from pathlib import Path
import hashlib
import time

def setup_msa_model_structure():
    """设置MSA模型的目录结构"""
    
    cache_dir = Path.home() / ".cache" / "huggingface" / "hub"
    model_dir = cache_dir / "models--katielink--esm_msa1b_t12_100M_UR50S"
    
    print("=" * 80)
    print("MSA Transformer模型设置指南")
    print("=" * 80)
    
    print(f"\n📁 目标目录: {model_dir}")
    
    if model_dir.exists():
        print("✅ MSA模型目录已存在")
        
        # 检查文件完整性
        snapshot_dirs = list((model_dir / "snapshots").glob("*"))
        if snapshot_dirs:
            snapshot_dir = snapshot_dirs[0]
            required_files = [
                "config.json",
                "pytorch_model.bin",  # 或 model.safetensors
                "tokenizer_config.json",
                "special_tokens_map.json",
                "vocab.txt"
            ]
            
            missing_files = []
            for file in required_files:
                if not (snapshot_dir / file).exists():
                    # 检查是否有.safetensors版本
                    if file == "pytorch_model.bin" and (snapshot_dir / "model.safetensors").exists():
                        continue
                    missing_files.append(file)
            
            if missing_files:
                print(f"❌ 缺少文件: {missing_files}")
                return False
            else:
                print("✅ 所有必需文件都存在")
                return True
        else:
            print("❌ snapshots目录为空")
            return False
    else:
        print("❌ MSA模型目录不存在")
        return False

def create_manual_setup_guide():
    """创建手动设置指南"""
    
    cache_dir = Path.home() / ".cache" / "huggingface" / "hub"
    model_dir = cache_dir / "models--katielink--esm_msa1b_t12_100M_UR50S"

    print("\n" + "=" * 80)
    print("手动下载和设置指南")
    print("=" * 80)

    print("\n🔗 HuggingFace模型页面:")
    print("https://huggingface.co/katielink/esm_msa1b_t12_100M_UR50S")
    
    print("\n📥 需要下载的文件:")
    files_to_download = [
        ("config.json", "模型配置文件"),
        ("pytorch_model.bin", "PyTorch模型权重文件"),
        ("tokenizer_config.json", "分词器配置"),
        ("special_tokens_map.json", "特殊token映射"),
        ("vocab.txt", "词汇表文件")
    ]
    
    for filename, description in files_to_download:
        print(f"  • {filename:<25} - {description}")
    
    print(f"\n📂 创建目录结构:")
    print(f"mkdir -p {model_dir}/refs")
    print(f"mkdir -p {model_dir}/snapshots")
    print(f"mkdir -p {model_dir}/blobs")
    
    # 生成一个commit hash
    commit_hash = hashlib.sha1(str(time.time()).encode()).hexdigest()
    snapshot_dir = model_dir / "snapshots" / commit_hash
    
    print(f"\n📁 创建snapshot目录:")
    print(f"mkdir -p {snapshot_dir}")
    
    print(f"\n📋 手动设置步骤:")
    print("1. 访问HuggingFace模型页面")
    print("2. 下载上述所有文件")
    print(f"3. 将文件放入: {snapshot_dir}/")
    print(f"4. 创建refs文件: echo '{commit_hash}' > {model_dir}/refs/main")
    
    print(f"\n🔧 自动创建目录结构:")
    try:
        # 创建目录结构
        model_dir.mkdir(parents=True, exist_ok=True)
        (model_dir / "refs").mkdir(exist_ok=True)
        (model_dir / "snapshots").mkdir(exist_ok=True)
        (model_dir / "blobs").mkdir(exist_ok=True)
        snapshot_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建refs/main文件
        with open(model_dir / "refs" / "main", "w") as f:
            f.write(commit_hash)
        
        print(f"✅ 目录结构已创建: {model_dir}")
        print(f"✅ Snapshot目录: {snapshot_dir}")
        print(f"✅ 请将下载的文件放入snapshot目录中")
        
        return snapshot_dir
        
    except Exception as e:
        print(f"❌ 创建目录失败: {e}")
        return None

def test_msa_model():
    """测试MSA模型是否可用"""
    
    print("\n" + "=" * 80)
    print("测试MSA模型")
    print("=" * 80)
    
    try:
        from transformers import EsmModel, EsmTokenizer
        
        print("正在测试MSA Transformer加载...")
        tokenizer = EsmTokenizer.from_pretrained(
            "katielink/esm_msa1b_t12_100M_UR50S",
            local_files_only=True
        )
        model = EsmModel.from_pretrained(
            "katielink/esm_msa1b_t12_100M_UR50S",
            local_files_only=True
        )
        
        print("✅ MSA Transformer加载成功!")
        print(f"模型参数量: {sum(p.numel() for p in model.parameters()):,}")
        print(f"隐藏层维度: {model.config.hidden_size}")
        
        return True
        
    except Exception as e:
        print(f"❌ MSA模型测试失败: {e}")
        return False

def show_current_status():
    """显示当前状态"""
    
    print("\n" + "=" * 80)
    print("当前状态")
    print("=" * 80)
    
    # 检查ESM-2模型
    esm2_dir = Path.home() / ".cache" / "huggingface" / "hub" / "models--facebook--esm2_t33_650M_UR50D"
    if esm2_dir.exists():
        print("✅ ESM-2模型已安装")
    else:
        print("❌ ESM-2模型未安装")
    
    # 检查MSA模型
    msa_dir = Path.home() / ".cache" / "huggingface" / "hub" / "models--katielink--esm_msa1b_t12_100M_UR50S"
    if msa_dir.exists():
        print("✅ MSA Transformer目录存在")
        if setup_msa_model_structure():
            print("✅ MSA Transformer完整安装")
        else:
            print("⚠️  MSA Transformer安装不完整")
    else:
        print("❌ MSA Transformer未安装")

def main():
    """主函数"""
    
    print("MSA Transformer模型设置工具")
    
    # 显示当前状态
    show_current_status()
    
    # 检查MSA模型
    if not setup_msa_model_structure():
        print("\n需要设置MSA模型...")
        snapshot_dir = create_manual_setup_guide()
        
        if snapshot_dir:
            print(f"\n📝 下载完成后，请将文件放入:")
            print(f"   {snapshot_dir}/")
            print("\n然后重新运行此脚本进行测试")
    else:
        # 测试模型
        if test_msa_model():
            print("\n🎉 MSA Transformer已准备就绪!")
        else:
            print("\n⚠️  MSA模型文件存在但无法加载，请检查文件完整性")

if __name__ == "__main__":
    main()

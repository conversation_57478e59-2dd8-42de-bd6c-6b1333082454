#!/usr/bin/env python3
"""
监控KcatData处理进度
"""

import os
import time
import pandas as pd
from datetime import datetime

def monitor_progress():
    """监控处理进度"""
    
    output_file = "kcatData_20250625.csv"
    total_sequences = 18265
    
    print("KcatData处理进度监控")
    print("=" * 50)
    print(f"目标文件: {output_file}")
    print(f"总序列数: {total_sequences:,}")
    print("=" * 50)
    
    try:
        while True:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 检查文件
            if os.path.exists(output_file):
                try:
                    # 读取文件行数
                    with open(output_file, 'r') as f:
                        processed_count = sum(1 for line in f) - 1  # 减去标题行
                    
                    progress_percent = (processed_count / total_sequences) * 100
                    remaining = total_sequences - processed_count
                    
                    # 估算剩余时间（假设每个序列平均2秒）
                    estimated_remaining_seconds = remaining * 2
                    remaining_hours = estimated_remaining_seconds // 3600
                    remaining_minutes = (estimated_remaining_seconds % 3600) // 60
                    
                    print(f"\n[TIME] {current_time}")
                    print(f"[PROGRESS] 处理进度: {processed_count:,}/{total_sequences:,} ({progress_percent:.1f}%)")
                    print(f"[BAR] 进度条: {'█' * int(progress_percent/2):<50} {progress_percent:.1f}%")
                    print(f"[REMAIN] 剩余序列: {remaining:,}")
                    print(f"[ETA] 预计剩余时间: {int(remaining_hours)}小时 {int(remaining_minutes)}分钟")
                    
                    # 检查最近几行
                    try:
                        df = pd.read_csv(output_file)
                        if len(df) > 0:
                            print(f"[UPDATE] 最后更新: {df['ID'].iloc[-1] if 'ID' in df.columns else 'N/A'}")
                            
                            # 检查成功率
                            if 'success' in df.columns:
                                success_count = df['success'].sum()
                                success_rate = (success_count / len(df)) * 100
                                print(f"[SUCCESS_RATE] 成功率: {success_count}/{len(df)} ({success_rate:.1f}%)")
                        
                        if processed_count >= total_sequences:
                            print("\n[SUCCESS] 处理完成!")
                            break
                            
                    except Exception as e:
                        print(f"[WARNING] 读取文件详情失败: {e}")
                        
                except Exception as e:
                    print(f"[ERROR] 读取文件失败: {e}")
            else:
                print(f"[WAIT] 等待处理开始... (输出文件不存在)")
            
            # 等待30秒后再次检查
            time.sleep(30)
            
    except KeyboardInterrupt:
        print("\n[STOP] 监控已停止")
    except Exception as e:
        print(f"[ERROR] 监控错误: {e}")

def check_current_status():
    """检查当前状态"""
    
    output_file = "kcatData_20250625.csv"
    total_sequences = 18265
    
    print("当前处理状态")
    print("=" * 40)
    
    # 检查文件
    if os.path.exists(output_file):
        # 读取文件行数
        with open(output_file, 'r') as f:
            processed_count = sum(1 for line in f) - 1
        
        progress_percent = (processed_count / total_sequences) * 100
        
        print(f"[PASS] 输出文件存在: {output_file}")
        print(f"[PROGRESS] 已处理序列: {processed_count:,}/{total_sequences:,}")
        print(f"[PERCENT] 完成度: {progress_percent:.1f}%")
        
        # 检查最近几行
        try:
            df = pd.read_csv(output_file)
            print(f"[COLUMNS] 文件列名: {list(df.columns)}")
            if len(df) > 0:
                print(f"[LAST] 最后5个ID: {df['ID'].tail().tolist()}")
                
                # 检查成功率
                if 'success' in df.columns:
                    success_count = df['success'].sum()
                    success_rate = (success_count / len(df)) * 100
                    print(f"[SUCCESS_RATE] 成功率: {success_count}/{len(df)} ({success_rate:.1f}%)")
                
        except Exception as e:
            print(f"[WARNING] 读取文件详情失败: {e}")
    else:
        print(f"[ERROR] 输出文件不存在: {output_file}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "status":
        check_current_status()
    else:
        monitor_progress()

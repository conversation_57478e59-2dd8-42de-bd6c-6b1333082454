#!/usr/bin/env python3
"""
example_usage.py - seq2attention函数的使用示例

演示如何在其他程序中调用seq2attention函数来提取蛋白质序列的attention融合特征
"""

import numpy as np
import pandas as pd
from seq2attention import seq2attention, batch_seq2attention, get_model_info

def example_single_sequence():
    """示例1: 处理单个蛋白质序列"""
    print("=" * 60)
    print("示例1: 处理单个蛋白质序列")
    print("=" * 60)

    # 示例蛋白质序列
    sequence = "MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPKKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD"

    print(f"输入序列: {sequence[:50]}...（长度: {len(sequence)}）")

    # 提取特征向量
    vector = seq2attention(sequence, verbose=True)

    print(f"输出特征向量维度: {vector.shape}")
    print(f"数值范围: [{vector.min():.3f}, {vector.max():.3f}]")
    print(f"平均值: {vector.mean():.3f}")
    print(f"标准差: {vector.std():.3f}")
    print(f"前10个值: {vector[:10]}")

    return vector

def example_multiple_sequences():
    """示例2: 批量处理多个蛋白质序列"""
    print("\n" + "=" * 60)
    print("示例2: 批量处理多个蛋白质序列")
    print("=" * 60)

    # 多个示例序列
    sequences = [
        "ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWY",  # 短序列
        "MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPKKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD",  # 长序列
        "MKLLVLSLCFLAVFTVFMSISSHAKGFKQVDQAIDQINQKWGKGVTQIKVVQFQKGQKYVVYRVDTQAYHAHTATQKTVDGPSGKLWRDGRGAAQNIIPASTGAAKAVGKVIPELNGKLTGMAFRVPTPNVSVVDLTCRLEKPAKYDDIKKVVKQASEGPLKGILGYTEHQVVSSDFNSDTHSSTFDAGAGIALNDHFVKLISWYDNEFGYSNRVVDLMAHMASKE",  # 中等长度序列
    ]

    sequence_names = ["短序列", "长序列", "中等长度序列"]

    print(f"处理 {len(sequences)} 个序列:")
    for i, (name, seq) in enumerate(zip(sequence_names, sequences)):
        print(f"  {i+1}. {name}: {seq[:30]}... (长度: {len(seq)})")

    # 批量提取特征
    vectors = batch_seq2attention(sequences, verbose=True)

    print(f"\n批量处理结果:")
    for i, (name, vector) in enumerate(zip(sequence_names, vectors)):
        if vector is not None:
            print(f"  {i+1}. {name}: 成功提取 {vector.shape} 维特征向量")
            print(f"     数值范围: [{vector.min():.3f}, {vector.max():.3f}], 平均值: {vector.mean():.3f}")
        else:
            print(f"  {i+1}. {name}: 处理失败")

    return vectors

def example_dataframe_processing():
    """示例3: 处理DataFrame中的序列数据"""
    print("\n" + "=" * 60)
    print("示例3: 处理DataFrame中的序列数据")
    print("=" * 60)

    # 创建示例DataFrame
    data = {
        'protein_id': ['P001', 'P002', 'P003'],
        'protein_name': ['Protein A', 'Protein B', 'Protein C'],
        'sequence': [
            "ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWY",
            "MKLLVLSLCFLAVFTVFMSISSHAKGFKQVDQAIDQINQKWGKGVTQIKVVQFQKGQKYVVYRVDTQAYHAHTATQKTVDGPSGKLWRDGRGAAQNIIPASTGAAKAVGKVIPELNGKLTGMAFRVPTPNVSVVDLTCRLEKPAKYDDIKKVVKQASEGPLKGILGYTEHQVVSSDFNSDTHSSTFDAGAGIALNDHFVKLISWYDNEFGYSNRVVDLMAHMASKE",
            "MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPKKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD"
        ]
    }

    df = pd.DataFrame(data)
    print("原始DataFrame:")
    print(df[['protein_id', 'protein_name']].to_string(index=False))
    print(f"序列长度: {[len(seq) for seq in df['sequence']]}")

    # 为每个序列提取特征向量
    print("\n提取特征向量...")
    vectors = []
    for idx, row in df.iterrows():
        try:
            vector = seq2attention(row['sequence'])
            vectors.append(vector)
            print(f"  {row['protein_id']} ({row['protein_name']}): 成功")
        except Exception as e:
            print(f"  {row['protein_id']} ({row['protein_name']}): 失败 - {e}")
            vectors.append(None)

    # 将特征向量添加到DataFrame
    df['attention_vector'] = vectors
    df['vector_dim'] = [len(v) if v is not None else 0 for v in vectors]

    print(f"\n处理结果:")
    result_df = df[['protein_id', 'protein_name', 'vector_dim']].copy()
    print(result_df.to_string(index=False))

    return df

def example_similarity_analysis():
    """示例4: 使用特征向量进行相似性分析"""
    print("\n" + "=" * 60)
    print("示例4: 使用特征向量进行相似性分析")
    print("=" * 60)

    # 两个相似的序列（一个是另一个的突变体）
    wild_type = "MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPKKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD"
    mutant = "MPIRVPDELPAVNFLREENVFVMTTSRASGQEIRPLKVLILNLMPAKIETENQFLRLLSNSPLQVDIQLLRIDSRESRNTPAEHLNNFYCNFEDIQDQNFDGLIVTGAPLGLVEFNDVAYWPQIKQVLEWSKDHVTSTLFVCWAVQAALNILYGIPKQTRTEKLSGVYEHHILHPHALLTRGFDDSFLAPHSRYADFPAALIRDYTDLEILAETEEGDAYLFASKDKRIAFVTGHPEYDAQTLAQEFFRDVEAGLDPDVPYNYFPHNDPQNTPRASWRSHGNLLFTNWLNYYVYQITPYDLRHMNPTLD"

    print("比较野生型和突变体蛋白质:")
    print(f"野生型: {wild_type[:50]}...")
    print(f"突变体: {mutant[:50]}...")

    # 找出差异位置
    differences = []
    for i, (a, b) in enumerate(zip(wild_type, mutant)):
        if a != b:
            differences.append((i+1, a, b))

    print(f"发现 {len(differences)} 个氨基酸差异:")
    for pos, wt_aa, mut_aa in differences:
        print(f"  位置 {pos}: {wt_aa} -> {mut_aa}")

    # 提取特征向量
    print("\n提取特征向量...")
    wt_vector = seq2attention(wild_type)
    mut_vector = seq2attention(mutant)

    # 计算相似性
    from scipy.spatial.distance import cosine
    cosine_similarity = 1 - cosine(wt_vector, mut_vector)
    euclidean_distance = np.linalg.norm(wt_vector - mut_vector)

    print(f"\n相似性分析结果:")
    print(f"  余弦相似度: {cosine_similarity:.6f}")
    print(f"  欧几里得距离: {euclidean_distance:.6f}")
    print(f"  特征向量差异的平均值: {np.mean(np.abs(wt_vector - mut_vector)):.6f}")
    print(f"  特征向量差异的标准差: {np.std(wt_vector - mut_vector):.6f}")

def main():
    """主函数：运行所有示例"""
    print("seq2attention函数使用示例")
    print("=" * 60)

    # 显示模型信息
    model_info = get_model_info()
    print("模型配置信息:")
    for key, value in model_info.items():
        print(f"  {key}: {value}")

    try:
        # 运行示例
        example_single_sequence()
        example_multiple_sequences()
        example_dataframe_processing()
        example_similarity_analysis()

        print("\n" + "=" * 60)
        print("[SUCCESS] 所有示例运行完成！")
        print("=" * 60)

    except Exception as e:
        print(f"\n[ERROR] 示例运行失败: {e}")

if __name__ == "__main__":
    main()
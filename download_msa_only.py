#!/usr/bin/env python3
"""
专门下载Facebook官方MSA模型
"""

import os
from pathlib import Path

def download_facebook_msa():
    """下载Facebook官方MSA模型"""
    
    print("📥 下载Facebook官方MSA模型...")
    print("模型: facebook/esm_msa1b_t12_100M_UR50S")
    
    try:
        import esm
        print("使用fair-esm库下载模型...")
        
        # 这会自动从Facebook官方源下载模型
        print("正在下载MSA Transformer模型...")
        model, alphabet = esm.pretrained.esm_msa1b_t12_100M_UR50S()
        
        print("✅ Facebook MSA模型下载成功!")
        print(f"模型参数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 检查下载的文件
        torch_cache = Path.home() / ".cache" / "torch" / "hub" / "checkpoints"
        msa_file = torch_cache / "esm_msa1b_t12_100M_UR50S.pt"
        contact_file = torch_cache / "esm_msa1b_t12_100M_UR50S-contact-regression.pt"
        
        if msa_file.exists():
            size_mb = msa_file.stat().st_size / (1024 * 1024)
            print(f"主模型文件: {msa_file} ({size_mb:.1f} MB)")
        
        if contact_file.exists():
            size_mb = contact_file.stat().st_size / (1024 * 1024)
            print(f"接触预测文件: {contact_file} ({size_mb:.1f} MB)")
        
        return True
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_msa_integration():
    """测试MSA模型与项目的集成"""
    
    print("\n🧪 测试MSA模型集成...")
    
    try:
        from feature_extractor import ProteinFeatureExtractor
        
        print("初始化特征提取器...")
        extractor = ProteinFeatureExtractor()
        
        # 检查MSA模型是否正确加载
        if hasattr(extractor, 'msa_extractor') and extractor.msa_extractor.msa_model is not None:
            if hasattr(extractor.msa_extractor, 'use_fair_esm_msa') and extractor.msa_extractor.use_fair_esm_msa:
                print("✅ 使用fair-esm加载的Facebook MSA模型")
                print(f"MSA特征维度: {extractor.msa_extractor.msa_dim}")
            else:
                print("⚠️  使用transformers库加载的MSA模型")
        else:
            print("❌ MSA模型未正确加载")
            return False
        
        print("测试序列特征提取...")
        sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
        features = extractor.extract_features(sequence)
        
        # 检查MSA特征
        msa_features = features["msa"]
        if "query_representation" in msa_features:
            query_repr = msa_features["query_representation"]
            print(f"✅ MSA特征提取成功!")
            print(f"MSA特征形状: {query_repr.shape}")
            print(f"MSA特征维度: {query_repr.shape[-1]}")
            
            if query_repr.shape[-1] == 768:
                print("✅ 确认使用768维MSA Transformer特征")
            else:
                print(f"⚠️  使用{query_repr.shape[-1]}维特征（可能是传统MSA特征）")
        else:
            print("❌ 未找到MSA query_representation")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_download_status():
    """显示下载状态"""
    
    print("\n📊 下载状态检查:")
    print("=" * 40)
    
    # 检查torch hub缓存
    torch_cache = Path.home() / ".cache" / "torch" / "hub" / "checkpoints"
    msa_files = [
        "esm_msa1b_t12_100M_UR50S.pt",
        "esm_msa1b_t12_100M_UR50S-contact-regression.pt"
    ]
    
    for file in msa_files:
        file_path = torch_cache / file
        if file_path.exists():
            size_mb = file_path.stat().st_size / (1024 * 1024)
            print(f"✅ {file}: {size_mb:.1f} MB")
        else:
            print(f"❌ {file}: 未找到")

def main():
    """主函数"""
    
    print("Facebook MSA模型下载工具")
    print("=" * 40)
    
    # 显示当前状态
    show_download_status()
    
    # 下载模型
    print("\n开始下载Facebook官方MSA模型...")
    if download_facebook_msa():
        print("\n🎉 MSA模型下载完成!")
        
        # 测试集成
        if test_msa_integration():
            print("\n✅ MSA模型已成功集成到项目中!")
        else:
            print("\n⚠️  MSA模型下载成功但集成测试失败")
    else:
        print("\n❌ MSA模型下载失败")
    
    # 显示最终状态
    print("\n" + "=" * 40)
    print("最终状态:")
    show_download_status()

if __name__ == "__main__":
    main()

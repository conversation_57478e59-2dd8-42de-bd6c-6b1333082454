#!/usr/bin/env python3
"""
模型下载和设置脚本
用于设置attention_fusion项目所需的预训练模型
"""

import os
import sys
from pathlib import Path

def show_model_structure():
    """显示需要的模型目录结构"""
    
    cache_dir = Path.home() / ".cache" / "huggingface" / "hub"
    
    print("=" * 80)
    print("Attention Fusion 项目模型设置指南")
    print("=" * 80)
    
    print(f"\n📁 HuggingFace缓存目录: {cache_dir}")
    
    print("\n🔧 需要下载的模型:")
    
    models = [
        {
            "name": "ESM-2 (650M参数)",
            "hf_name": "facebook/esm2_t33_650M_UR50D",
            "cache_name": "models--facebook--esm2_t33_650M_UR50D",
            "size": "~2.5GB",
            "description": "蛋白质序列编码器"
        },
        {
            "name": "MSA Transformer (100M参数)", 
            "hf_name": "facebook/esm_msa1b_t12_100M_UR50S",
            "cache_name": "models--facebook--esm_msa1b_t12_100M_UR50S",
            "size": "~400MB",
            "description": "多序列比对特征提取器"
        }
    ]
    
    for i, model in enumerate(models, 1):
        print(f"\n{i}. {model['name']}")
        print(f"   HuggingFace名称: {model['hf_name']}")
        print(f"   缓存目录名: {model['cache_name']}")
        print(f"   大小: {model['size']}")
        print(f"   用途: {model['description']}")
        
        model_path = cache_dir / model['cache_name']
        if model_path.exists():
            print(f"   状态: ✅ 已存在")
        else:
            print(f"   状态: ❌ 需要下载")
            print(f"   目标路径: {model_path}")
    
    print("\n" + "=" * 80)
    print("手动下载步骤:")
    print("=" * 80)
    
    print("\n方法1: 使用git lfs下载")
    print("1. 安装git-lfs: sudo apt install git-lfs")
    print("2. 创建缓存目录:")
    print(f"   mkdir -p {cache_dir}")
    print("3. 下载模型:")
    
    for model in models:
        model_path = cache_dir / model['cache_name']
        print(f"\n   # 下载 {model['name']}")
        print(f"   cd {cache_dir}")
        print(f"   git lfs clone https://huggingface.co/{model['hf_name']} {model['cache_name']}")
    
    print("\n方法2: 使用huggingface-hub下载")
    print("1. 安装: pip install huggingface-hub")
    print("2. 运行Python脚本:")
    
    script_content = '''
from huggingface_hub import snapshot_download
import os

# 设置缓存目录
cache_dir = "/home/<USER>/.cache/huggingface/hub"
os.makedirs(cache_dir, exist_ok=True)

models = [
    "facebook/esm2_t33_650M_UR50D",
    "facebook/esm_msa1b_t12_100M_UR50S"
]

for model_name in models:
    print(f"下载 {model_name}...")
    try:
        snapshot_download(
            repo_id=model_name,
            cache_dir=cache_dir,
            resume_download=True
        )
        print(f"✅ {model_name} 下载完成")
    except Exception as e:
        print(f"❌ {model_name} 下载失败: {e}")
'''
    
    print(script_content)
    
    print("\n方法3: 手动放置文件")
    print("如果您已经下载了模型文件，请按以下结构放置:")
    
    for model in models:
        model_path = cache_dir / model['cache_name']
        print(f"\n{model_path}/")
        print("├── refs/")
        print("│   └── main")
        print("├── snapshots/")
        print("│   └── [commit_hash]/")
        print("│       ├── config.json")
        print("│       ├── pytorch_model.bin (或 .safetensors)")
        print("│       ├── tokenizer.json")
        print("│       ├── tokenizer_config.json")
        print("│       └── vocab.txt")
        print("└── blobs/")
        print("    └── [各种文件的blob]")

def check_models():
    """检查模型是否已正确安装"""
    
    print("\n[CHECK] 检查模型安装状态...")
    
    try:
        # 激活conda环境并检查
        import subprocess
        result = subprocess.run([
            "conda", "run", "-n", "attention_fusion", "python", "-c",
            """
import torch
from transformers import EsmModel, EsmTokenizer

models_to_check = [
    'facebook/esm2_t33_650M_UR50D',
    'facebook/esm_msa1b_t12_100M_UR50S'
]

for model_name in models_to_check:
    try:
        print(f'检查 {model_name}...')
        tokenizer = EsmTokenizer.from_pretrained(model_name, local_files_only=True)
        model = EsmModel.from_pretrained(model_name, local_files_only=True)
        print(f'[PASS] {model_name} 可用')
    except Exception as e:
        print(f'[FAIL] {model_name} 不可用: {e}')
            """
        ], capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("错误信息:", result.stderr)
            
    except Exception as e:
        print(f"检查失败: {e}")

def create_download_script():
    """创建自动下载脚本"""
    
    script_path = Path("download_models.py")
    
    download_script = '''#!/usr/bin/env python3
"""
自动下载attention_fusion所需的模型
"""

import os
import sys
from pathlib import Path

def download_models():
    """下载所需模型"""
    
    try:
        from huggingface_hub import snapshot_download
    except ImportError:
        print("请先安装huggingface-hub: pip install huggingface-hub")
        return False
    
    # 模型列表
    models = [
        "facebook/esm2_t33_650M_UR50D",
        "facebook/esm_msa1b_t12_100M_UR50S"
    ]
    
    cache_dir = Path.home() / ".cache" / "huggingface" / "hub"
    cache_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"下载目录: {cache_dir}")
    
    for model_name in models:
        print(f"\\n正在下载 {model_name}...")
        try:
            snapshot_download(
                repo_id=model_name,
                cache_dir=str(cache_dir),
                resume_download=True,
                local_files_only=False
            )
            print(f"[SUCCESS] {model_name} 下载完成")
        except Exception as e:
            print(f"[ERROR] {model_name} 下载失败: {e}")
            return False
    
    print("\\n[SUCCESS] 所有模型下载完成!")
    return True

if __name__ == "__main__":
    success = download_models()
    sys.exit(0 if success else 1)
'''
    
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(download_script)
    
    # 设置执行权限
    os.chmod(script_path, 0o755)
    
    print(f"\n📝 已创建下载脚本: {script_path}")
    print("运行方式:")
    print(f"  conda activate attention_fusion")
    print(f"  python {script_path}")

if __name__ == "__main__":
    show_model_structure()
    check_models()
    create_download_script()

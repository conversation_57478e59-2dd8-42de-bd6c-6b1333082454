#!/usr/bin/env python3
"""
utils.py 简化演示
展示核心功能的使用方法
"""

import torch
import numpy as np
import os
from pathlib import Path

# 导入项目模块
from feature_extractor import ProteinFeatureExtractor
from attention_fusion_model import AttentionFusionModel
import utils

def main():
    """主演示函数"""
    
    print("🧰 Utils.py 核心功能演示")
    print("=" * 50)
    
    # 1. 初始化模型
    print("1. 初始化模型...")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    extractor = ProteinFeatureExtractor()
    model = AttentionFusionModel(esm_dim=1280, msa_dim=768, hidden_dim=512, num_blocks=2, num_heads=4)
    model.to(device)
    
    # 2. 准备测试序列
    sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
    print(f"2. 测试序列长度: {len(sequence)}")
    
    # 3. 提取特征和运行模型
    print("3. 提取特征并运行模型...")
    all_features = extractor.extract_features(sequence)
    esm_features = all_features['esm2']['residue_embeddings'].unsqueeze(0).to(device)
    msa_features = all_features['msa']
    
    results = model(esm_features, msa_features)
    
    # 4. 输入验证演示
    print("\n4. 输入验证演示:")
    print("=" * 30)
    
    # 有效输入
    is_valid = utils.validate_attention_fusion_input(esm_features, msa_features)
    print(f"   有效输入测试: {'[PASS] 通过' if is_valid else '[FAIL] 失败'}")
    
    # 无效输入
    invalid_esm = torch.randn(50, 1280)  # 缺少batch维度
    is_valid = utils.validate_attention_fusion_input(invalid_esm, msa_features)
    print(f"   无效输入测试: {'[PASS] 通过' if is_valid else '[FAIL] 失败'} (预期失败)")
    
    # 5. 注意力分析演示
    print("\n5. 注意力分析演示:")
    print("=" * 30)
    
    # 打印注意力摘要
    utils.print_attention_summary(results)
    
    # 6. 特征相似性分析演示
    print("\n6. 特征相似性分析演示:")
    print("=" * 30)
    
    fused_features = results["fused_features"]
    enhanced_esm = results["enhanced_esm_features"]
    enhanced_msa = results["enhanced_msa_features"]
    
    # 余弦相似性
    sim_result = utils.compute_feature_similarity(fused_features, enhanced_esm, method="cosine")
    print("   融合特征 vs 增强ESM特征 (余弦相似性):")
    for key, value in sim_result.items():
        print(f"     {key}: {value:.4f}")
    
    # L2距离
    dist_result = utils.compute_feature_similarity(fused_features, enhanced_esm, method="l2")
    print("   融合特征 vs 增强ESM特征 (L2距离):")
    for key, value in dist_result.items():
        print(f"     {key}: {value:.4f}")
    
    # 7. 保存和加载演示
    print("\n7. 保存和加载演示:")
    print("=" * 30)
    
    # 创建输出目录
    output_dir = Path("demo_output")
    output_dir.mkdir(exist_ok=True)
    
    # 保存分析结果
    save_path = str(output_dir / "demo_analysis.npz")
    utils.save_attention_analysis(results, save_path)
    print(f"   [SAVE] 分析结果已保存到: {save_path}")
    
    # 加载分析结果
    loaded_results = utils.load_attention_analysis(save_path)
    print(f"   [LOAD] 分析结果已加载")
    
    # 验证数据一致性
    original_shape = results["fused_features"].shape
    loaded_shape = loaded_results["fused_features"].shape
    print(f"   数据验证: 原始 {original_shape} -> 加载 {loaded_shape}")
    
    if torch.allclose(results["fused_features"].cpu(), loaded_results["fused_features"], atol=1e-6):
        print("   [PASS] 数据一致性验证通过")
    else:
        print("   [FAIL] 数据一致性验证失败")
    
    # 8. 可视化功能演示 (不显示图像，只保存)
    print("\n8. 可视化功能演示:")
    print("=" * 30)
    
    if "attention_weights" in results and results["attention_weights"]:
        attention_weights = results["attention_weights"]
        first_layer = attention_weights[0]
        
        if "esm_to_msa_weights" in first_layer:
            esm_to_msa = first_layer["esm_to_msa_weights"].squeeze(0)
            
            # 保存注意力权重可视化（不显示）
            import matplotlib
            matplotlib.use('Agg')  # 使用非交互式后端
            
            utils.visualize_attention_weights(
                esm_to_msa, 
                sequence, 
                title="ESM-2 to MSA Attention",
                save_path=str(output_dir / "attention_heatmap.png")
            )
            print("   [SAVE] 注意力权重热图已保存")
        
        if "esm_to_msa_weights" in first_layer and "msa_to_esm_weights" in first_layer:
            utils.visualize_cross_attention_flow(
                first_layer["esm_to_msa_weights"].squeeze(0),
                first_layer["msa_to_esm_weights"].squeeze(0),
                sequence,
                save_path=str(output_dir / "cross_attention.png")
            )
            print("   [SAVE] 交叉注意力流图已保存")
    
    # 特征演化可视化
    features_by_layer = [
        results["enhanced_esm_features"].squeeze(0),
        results["fused_features"].squeeze(0)
    ]
    
    utils.plot_feature_evolution(
        features_by_layer,
        sequence,
        save_path=str(output_dir / "feature_evolution.png")
    )
    print("   [SAVE] 特征演化图已保存")
    
    # 9. 总结
    print("\n" + "=" * 50)
    print("[SUCCESS] Utils.py 演示完成!")
    print("=" * 50)
    
    print("\n[LIST] Utils.py 主要功能:")
    print("  [SEARCH] validate_attention_fusion_input() - 输入验证")
    print("  [CHART] print_attention_summary() - 注意力分析摘要")
    print("  [LINK] compute_feature_similarity() - 特征相似性计算")
    print("  [SAVE] save_attention_analysis() - 保存分析结果")
    print("  [LOAD] load_attention_analysis() - 加载分析结果")
    print("  [ART] visualize_attention_weights() - 注意力权重可视化")
    print("  [FLOW] visualize_cross_attention_flow() - 交叉注意力流可视化")
    print("  [GRAPH] plot_feature_evolution() - 特征演化可视化")
    
    print(f"\n[FOLDER] 生成的文件位于: {output_dir}/")
    print("  - demo_analysis.npz (分析数据)")
    print("  - attention_heatmap.png (注意力热图)")
    print("  - cross_attention.png (交叉注意力)")
    print("  - feature_evolution.png (特征演化)")
    
    print("\n[TIP] 使用建议:")
    print("  1. 在处理蛋白质序列后，使用utils函数分析结果")
    print("  2. 使用可视化功能理解注意力模式")
    print("  3. 使用相似性分析比较不同特征")
    print("  4. 使用保存/加载功能管理分析结果")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
自动下载attention_fusion所需的模型
"""

import os
import sys
from pathlib import Path

def download_models():
    """下载所需模型"""
    
    try:
        from huggingface_hub import snapshot_download
    except ImportError:
        print("请先安装huggingface-hub: pip install huggingface-hub")
        return False
    
    # 模型列表
    models = [
        "facebook/esm2_t33_650M_UR50D",
        "facebook/esm_msa1b_t12_100M_UR50S"
    ]
    
    cache_dir = Path.home() / ".cache" / "huggingface" / "hub"
    cache_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"下载目录: {cache_dir}")
    
    for model_name in models:
        print(f"\n正在下载 {model_name}...")
        try:
            snapshot_download(
                repo_id=model_name,
                cache_dir=str(cache_dir),
                resume_download=True,
                local_files_only=False
            )
            print(f"✅ {model_name} 下载完成")
        except Exception as e:
            print(f"❌ {model_name} 下载失败: {e}")
            return False
    
    print("\n🎉 所有模型下载完成!")
    return True

if __name__ == "__main__":
    success = download_models()
    sys.exit(0 if success else 1)
